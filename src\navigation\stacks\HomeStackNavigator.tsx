import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Screens
import FeedScreen from '../../screens/main/FeedScreen';
import PostDetailScreen from '../../screens/main/PostDetailScreen';
import UserProfileScreen from '../../screens/profile/UserProfileScreen';
import CommentsScreen from '../../screens/main/CommentsScreen';
import LikesScreen from '../../screens/main/LikesScreen';
import StoriesScreen from '../../screens/main/StoriesScreen';

// Constants
import { Colors } from '../../constants/Colors';
import { FontSize, FontWeight, Spacing } from '../../constants/Layout';

// Types
import { HomeStackParamList } from '../../types/navigation';

const Stack = createStackNavigator<HomeStackParamList>();

const HomeStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.background,
          borderBottomWidth: 1,
          borderBottomColor: Colors.border,
          elevation: 0,
          shadowOpacity: 0,
        },
        headerTintColor: Colors.text,
        headerTitleStyle: {
          fontSize: FontSize.lg,
          fontWeight: FontWeight.semibold,
        },
        headerBackTitleVisible: false,
        headerLeftContainerStyle: {
          paddingLeft: Spacing.md,
        },
        headerRightContainerStyle: {
          paddingRight: Spacing.md,
        },
      }}
    >
      <Stack.Screen
        name="Feed"
        component={FeedScreen}
        options={{
          headerTitle: () => (
            <View style={styles.headerTitle}>
              <Text style={styles.logoText}>Woézon</Text>
            </View>
          ),
          headerLeft: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Ionicons name="camera-outline" size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View style={styles.headerRightContainer}>
              <TouchableOpacity style={styles.headerButton}>
                <Ionicons name="paper-plane-outline" size={24} color={Colors.text} />
              </TouchableOpacity>
              <TouchableOpacity style={[styles.headerButton, { marginLeft: Spacing.sm }]}>
                <Ionicons name="chatbubble-outline" size={24} color={Colors.text} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      
      <Stack.Screen
        name="PostDetail"
        component={PostDetailScreen}
        options={{
          headerTitle: 'Publication',
          headerRight: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Ionicons name="ellipsis-horizontal" size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
        }}
      />
      
      <Stack.Screen
        name="UserProfile"
        component={UserProfileScreen}
        options={({ route }) => ({
          headerTitle: route.params?.username || 'Profil',
          headerRight: () => (
            <View style={styles.headerRightContainer}>
              <TouchableOpacity style={styles.headerButton}>
                <Ionicons name="notifications-outline" size={24} color={Colors.text} />
              </TouchableOpacity>
              <TouchableOpacity style={[styles.headerButton, { marginLeft: Spacing.sm }]}>
                <Ionicons name="ellipsis-horizontal" size={24} color={Colors.text} />
              </TouchableOpacity>
            </View>
          ),
        })}
      />
      
      <Stack.Screen
        name="Comments"
        component={CommentsScreen}
        options={{
          headerTitle: 'Commentaires',
          headerRight: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Ionicons name="paper-plane-outline" size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
        }}
      />
      
      <Stack.Screen
        name="Likes"
        component={LikesScreen}
        options={{
          headerTitle: 'J\'aime',
        }}
      />
      
      <Stack.Screen
        name="Stories"
        component={StoriesScreen}
        options={{
          headerShown: false, // Stories en plein écran
        }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  headerTitle: {
    alignItems: 'center',
  },
  logoText: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'Roboto',
  },
  headerButton: {
    padding: Spacing.xs,
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default HomeStackNavigator;
