/**
 * Types de navigation pour Woézon
 * Structure Instagram-like avec navigation par onglets
 */

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Profile: { userId?: string };
  Chat: { conversationId?: string };
  CreatePost: undefined;
  PostDetail: { postId: string };
  UserProfile: { userId: string };
  Settings: undefined;
  EditProfile: undefined;
  LiveStream: { streamId?: string };
  Call: { callId: string; type: 'voice' | 'video' };
};

export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  VerifyEmail: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Search: undefined;
  Create: undefined;
  Activity: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  Feed: undefined;
  PostDetail: { postId: string };
  UserProfile: { userId: string };
  Comments: { postId: string };
  Likes: { postId: string };
  Stories: { userId?: string; storyIndex?: number };
};

export type SearchStackParamList = {
  Explore: undefined;
  SearchResults: { query: string };
  UserProfile: { userId: string };
  HashtagFeed: { hashtag: string };
  LocationFeed: { locationId: string };
};

export type CreateStackParamList = {
  CreatePost: undefined;
  Camera: undefined;
  EditPhoto: { imageUri: string };
  EditVideo: { videoUri: string };
  AddCaption: { mediaUri: string; mediaType: 'photo' | 'video' };
  SharePost: { postData: any };
  CreateStory: undefined;
  GoLive: undefined;
};

export type ActivityStackParamList = {
  Notifications: undefined;
  Following: undefined;
  You: undefined;
  Requests: undefined;
  FriendSuggestions: undefined;
};

export type ProfileStackParamList = {
  MyProfile: undefined;
  EditProfile: undefined;
  Settings: undefined;
  Privacy: undefined;
  Security: undefined;
  Help: undefined;
  About: undefined;
  Friends: undefined;
  Followers: undefined;
  Following: undefined;
  SavedPosts: undefined;
  Archive: undefined;
};

export type ChatStackParamList = {
  ConversationsList: undefined;
  Conversation: { conversationId: string };
  NewMessage: undefined;
  GroupChat: { groupId?: string };
  CallScreen: { callId: string; type: 'voice' | 'video' };
  AIChat: undefined;
};

export type LiveStackParamList = {
  LiveFeed: undefined;
  LiveStream: { streamId: string };
  CreateLive: undefined;
  LiveSettings: undefined;
};

// Types pour les paramètres de navigation
export type NavigationProps = {
  navigation: any;
  route: any;
};

// Types pour les données des écrans
export type PostData = {
  id: string;
  user_id: string;
  content?: string;
  image_url?: string;
  video_url?: string;
  post_type: 'post' | 'story' | 'reel';
  likes_count: number;
  comments_count: number;
  shares_count: number;
  views_count: number;
  created_at: string;
  user?: {
    id: string;
    username: string;
    full_name?: string;
    avatar_url?: string;
    is_verified: boolean;
  };
};

export type UserData = {
  id: string;
  username: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  website?: string;
  is_verified: boolean;
  is_private: boolean;
  followers_count: number;
  following_count: number;
  posts_count: number;
  friends_count: number;
  is_online: boolean;
  last_seen?: string;
};

export type ConversationData = {
  id: string;
  name?: string;
  is_group: boolean;
  participants: UserData[];
  last_message?: {
    content: string;
    created_at: string;
    sender: UserData;
  };
  unread_count: number;
};

export type NotificationData = {
  id: string;
  type: 'like' | 'comment' | 'follow' | 'friend_request' | 'mention' | 'live' | 'call';
  user: UserData;
  post?: PostData;
  message?: string;
  created_at: string;
  is_read: boolean;
};

export type LiveStreamData = {
  id: string;
  host: UserData;
  title: string;
  description?: string;
  viewers_count: number;
  is_active: boolean;
  started_at: string;
  thumbnail_url?: string;
};

export type CallData = {
  id: string;
  caller: UserData;
  participants: UserData[];
  call_type: 'voice' | 'video';
  is_group_call: boolean;
  status: 'initiated' | 'ringing' | 'answered' | 'ended' | 'missed' | 'rejected';
  started_at: string;
  ended_at?: string;
  duration_seconds: number;
};

// Types pour les hooks de navigation
export type UseNavigationHook = () => {
  navigate: (screen: string, params?: any) => void;
  goBack: () => void;
  reset: (routes: any[]) => void;
  canGoBack: () => boolean;
};

// Types pour les modals
export type ModalProps = {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
};

// Types pour les bottom sheets
export type BottomSheetProps = {
  isVisible: boolean;
  onClose: () => void;
  height?: number;
  children: React.ReactNode;
};

// Types pour les animations
export type AnimationConfig = {
  duration: number;
  easing?: string;
  delay?: number;
};

// Types pour les gestes
export type GestureConfig = {
  enabled: boolean;
  direction?: 'horizontal' | 'vertical';
  threshold?: number;
};

export default {
  RootStackParamList,
  AuthStackParamList,
  MainTabParamList,
  HomeStackParamList,
  SearchStackParamList,
  CreateStackParamList,
  ActivityStackParamList,
  ProfileStackParamList,
  ChatStackParamList,
  LiveStackParamList,
};
