import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Layout';

export default function HomeScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.logo}>Woézon</Text>
          <Text style={styles.subtitle}>Votre réseau social nouvelle génération</Text>
        </View>

        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Bienvenue sur Woézon ! 🎉</Text>
          <Text style={styles.welcomeText}>
            Découvrez une nouvelle façon de vous connecter avec le monde.
            Partagez vos moments, découvrez du contenu inspirant et 
            connectez-vous avec des personnes du monde entier...
          </Text>
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Fonctionnalités à venir</Text>
          
          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>📸 Partage de photos et vidéos</Text>
            <Text style={styles.featureDescription}>
              Partagez vos moments préférés avec une interface inspirée d'Instagram
            </Text>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>💬 Chat en temps réel</Text>
            <Text style={styles.featureDescription}>
              Discutez avec vos amis en privé ou en groupe
            </Text>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>🌍 Découverte mondiale</Text>
            <Text style={styles.featureDescription}>
              Connectez-vous avec des personnes du monde entier
            </Text>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>💼 Opportunités professionnelles</Text>
            <Text style={styles.featureDescription}>
              Trouvez des opportunités d'emploi et développez votre réseau
            </Text>
          </View>
        </View>

        {/* Status Section */}
        <View style={styles.statusSection}>
          <Text style={styles.statusTitle}>État du développement</Text>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>✅ Configuration de base</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>✅ Supabase configuré</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🔄 Interface utilisateur</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>⏳ Authentification</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>⏳ Fonctionnalités sociales</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  logo: {
    fontSize: FontSize.xxxl,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  welcomeSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  welcomeTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  welcomeText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    lineHeight: 24,
  },
  featuresSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.lg,
  },
  featureCard: {
    backgroundColor: Colors.backgroundSecondary,
    padding: Spacing.lg,
    borderRadius: 12,
    marginBottom: Spacing.md,
  },
  featureTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  featureDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
  statusSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  statusTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.lg,
  },
  statusItem: {
    marginBottom: Spacing.sm,
  },
  statusLabel: {
    fontSize: FontSize.md,
    color: Colors.text,
  },
});
