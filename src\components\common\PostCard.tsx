import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Constants
import { Colors } from '../../constants/Colors';
import { Spacing, FontSize, FontWeight, BorderRadius } from '../../constants/Layout';

// Types
import { PostData } from '../../types/navigation';

const { width: screenWidth } = Dimensions.get('window');

interface PostCardProps {
  post: PostData;
  onLike: () => void;
  onComment: () => void;
  onShare: () => void;
  onUserPress: () => void;
  onPress: () => void;
}

const PostCard: React.FC<PostCardProps> = ({
  post,
  onLike,
  onComment,
  onShare,
  onUserPress,
  onPress,
}) => {
  const [isLiked, setIsLiked] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleLike = () => {
    setIsLiked(!isLiked);
    onLike();
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}j`;
  };

  const formatCount = (count: number) => {
    if (count < 1000) return count.toString();
    if (count < 1000000) return `${(count / 1000).toFixed(1)}k`;
    return `${(count / 1000000).toFixed(1)}M`;
  };

  return (
    <View style={styles.container}>
      {/* Header avec profil utilisateur */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.userInfo} onPress={onUserPress}>
          <Image
            source={{ uri: post.user?.avatar_url || 'https://via.placeholder.com/40' }}
            style={styles.avatar}
          />
          <View style={styles.userDetails}>
            <View style={styles.usernameContainer}>
              <Text style={styles.username}>{post.user?.username}</Text>
              {post.user?.is_verified && (
                <Ionicons
                  name="checkmark-circle"
                  size={14}
                  color={Colors.verified}
                  style={styles.verifiedIcon}
                />
              )}
            </View>
            <Text style={styles.timeAgo}>{formatTimeAgo(post.created_at)}</Text>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-horizontal" size={20} color={Colors.text} />
        </TouchableOpacity>
      </View>

      {/* Image du post */}
      {post.image_url && (
        <TouchableOpacity onPress={onPress} activeOpacity={0.95}>
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: post.image_url }}
              style={styles.postImage}
              onLoad={() => setImageLoaded(true)}
              resizeMode="cover"
            />
            {!imageLoaded && (
              <View style={styles.imagePlaceholder}>
                <Ionicons name="image-outline" size={40} color={Colors.textSecondary} />
              </View>
            )}
          </View>
        </TouchableOpacity>
      )}

      {/* Actions (Like, Comment, Share) */}
      <View style={styles.actions}>
        <View style={styles.leftActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
            <Ionicons
              name={isLiked ? 'heart' : 'heart-outline'}
              size={24}
              color={isLiked ? Colors.like : Colors.text}
            />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={onComment}>
            <Ionicons name="chatbubble-outline" size={24} color={Colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={onShare}>
            <Ionicons name="paper-plane-outline" size={24} color={Colors.text} />
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity style={styles.saveButton}>
          <Ionicons name="bookmark-outline" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>

      {/* Nombre de likes */}
      {post.likes_count > 0 && (
        <TouchableOpacity style={styles.likesContainer}>
          <Text style={styles.likesText}>
            {formatCount(post.likes_count)} j'aime
          </Text>
        </TouchableOpacity>
      )}

      {/* Contenu du post */}
      {post.content && (
        <View style={styles.contentContainer}>
          <Text style={styles.content}>
            <Text style={styles.username}>{post.user?.username}</Text>
            <Text style={styles.contentText}> {post.content}</Text>
          </Text>
        </View>
      )}

      {/* Voir les commentaires */}
      {post.comments_count > 0 && (
        <TouchableOpacity style={styles.commentsContainer} onPress={onComment}>
          <Text style={styles.commentsText}>
            Voir les {post.comments_count} commentaires
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    marginBottom: Spacing.xs,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: Spacing.sm,
  },
  userDetails: {
    flex: 1,
  },
  usernameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  username: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  verifiedIcon: {
    marginLeft: Spacing.xs,
  },
  timeAgo: {
    fontSize: FontSize.xs,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  moreButton: {
    padding: Spacing.xs,
  },
  imageContainer: {
    width: screenWidth,
    height: screenWidth,
    backgroundColor: Colors.backgroundSecondary,
  },
  postImage: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.backgroundSecondary,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  leftActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    marginRight: Spacing.md,
    padding: Spacing.xs,
  },
  saveButton: {
    padding: Spacing.xs,
  },
  likesContainer: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.xs,
  },
  likesText: {
    fontSize: FontSize.sm,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
  },
  contentContainer: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.xs,
  },
  content: {
    fontSize: FontSize.sm,
    lineHeight: 18,
  },
  contentText: {
    color: Colors.text,
  },
  commentsContainer: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.sm,
  },
  commentsText: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
  },
});

export default PostCard;
