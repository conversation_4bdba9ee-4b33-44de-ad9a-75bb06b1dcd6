# WOÉZON - REQUIREMENTS ET DÉPENDANCES
# =====================================

# ENVIRONNEMENT DE DÉVELOPPEMENT
# ==============================
Node.js >= 18.0.0
npm >= 8.0.0
Expo CLI >= 6.0.0

# DÉPENDANCES PRINCIPALES REACT NATIVE
# ====================================
expo >= 49.0.0
react >= 18.2.0
react-native >= 0.72.0
react-dom >= 18.2.0
react-native-web >= 0.19.0

# NAVIGATION
# ==========
@react-navigation/native >= 6.1.0
@react-navigation/bottom-tabs >= 6.5.0
@react-navigation/stack >= 6.3.0
@react-navigation/drawer >= 6.6.0
react-native-screens >= 3.20.0
react-native-safe-area-context >= 4.5.0
react-native-gesture-handler >= 2.9.0

# INTERFACE UTILISATEUR
# ====================
@expo/vector-icons >= 13.0.0
react-native-svg >= 13.4.0
react-native-linear-gradient >= 2.6.2
react-native-animatable >= 1.3.3
react-native-reanimated >= 3.0.0
react-native-paper >= 5.8.0
styled-components >= 5.3.0

# BACKEND ET BASE DE DONNÉES
# ==========================
@supabase/supabase-js >= 2.26.0
@supabase/auth-helpers-react >= 0.4.0

# AUTHENTIFICATION
# ===============
expo-auth-session >= 5.0.0
expo-crypto >= 12.4.0
expo-secure-store >= 12.3.0

# MÉDIAS ET FICHIERS
# ==================
expo-image-picker >= 14.3.0
expo-camera >= 13.4.0
expo-av >= 13.4.0
expo-media-library >= 15.4.0
expo-file-system >= 15.4.0
expo-sharing >= 11.5.0

# GÉOLOCALISATION
# ===============
expo-location >= 16.1.0
react-native-maps >= 1.7.0

# NOTIFICATIONS
# =============
expo-notifications >= 0.20.0
expo-device >= 5.4.0

# COMMUNICATION TEMPS RÉEL
# ========================
socket.io-client >= 4.7.0
@react-native-async-storage/async-storage >= 1.19.0

# INTELLIGENCE ARTIFICIELLE
# =========================
openai >= 3.3.0
axios >= 1.4.0

# UTILITAIRES
# ===========
date-fns >= 2.30.0
lodash >= 4.17.21
uuid >= 9.0.0
react-hook-form >= 7.45.0
yup >= 1.2.0

# DÉVELOPPEMENT ET TESTS
# ======================
typescript >= 5.1.0
@types/react >= 18.2.0
@types/react-native >= 0.72.0
@types/lodash >= 4.14.0
@types/uuid >= 9.0.0
eslint >= 8.44.0
prettier >= 2.8.0
jest >= 29.5.0
@testing-library/react-native >= 12.1.0

# OUTILS DE BUILD
# ===============
@expo/metro-runtime >= 3.0.0
metro >= 0.76.0
babel-preset-expo >= 9.5.0

# SÉCURITÉ
# ========
expo-crypto >= 12.4.0
expo-random >= 14.0.0

# PERFORMANCE
# ===========
react-native-fast-image >= 8.6.0
react-native-performance >= 5.1.0

# ANALYTICS
# =========
expo-analytics-amplitude >= 1.0.0
@react-native-firebase/analytics >= 18.3.0

# DÉBOGAGE
# ========
react-native-flipper >= 0.212.0
reactotron-react-native >= 5.0.0

# CONFIGURATION MINIMALE SYSTÈME
# ==============================
OS: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
RAM: 8GB minimum, 16GB recommandé
Stockage: 10GB d'espace libre
Connexion Internet: Requise pour Supabase et OpenAI

# COMPTES ET SERVICES REQUIS
# ==========================
Compte Supabase (gratuit)
Clé API OpenAI
Compte Expo (gratuit)
Compte Google Play Console (pour Android)
Compte Apple Developer (pour iOS)

# VARIABLES D'ENVIRONNEMENT REQUISES
# ==================================
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
OPENAI_API_KEY=your_openai_api_key
APP_NAME=Woézon
APP_VERSION=1.0.0

# PORTS UTILISÉS
# ==============
8081 - Metro Bundler (principal)
8082 - Metro Bundler (alternatif)
19000 - Expo DevTools
19001 - Expo DevTools (alternatif)
19002 - Expo DevTools (alternatif)

# NAVIGATEURS SUPPORTÉS
# =====================
Chrome >= 90
Firefox >= 88
Safari >= 14
Edge >= 90

# APPAREILS MOBILES SUPPORTÉS
# ===========================
iOS >= 11.0
Android >= API 21 (Android 5.0)

# OUTILS DE DÉVELOPPEMENT RECOMMANDÉS
# ===================================
Visual Studio Code
Expo Go (mobile)
Android Studio (pour Android)
Xcode (pour iOS, macOS uniquement)
Postman (pour tester les API)
Supabase Dashboard
Git

# EXTENSIONS VS CODE RECOMMANDÉES
# ===============================
Expo Tools
React Native Tools
TypeScript Importer
Prettier
ESLint
GitLens
Thunder Client
Auto Rename Tag
Bracket Pair Colorizer
Material Icon Theme
