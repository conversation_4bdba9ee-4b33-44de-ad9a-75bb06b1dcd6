import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Components
import PostCard from '../../components/common/PostCard';
import StoriesBar from '../../components/common/StoriesBar';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

// Constants
import { Colors } from '../../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../../constants/Layout';

// Types
import { PostData, UserData } from '../../types/navigation';

const { width: screenWidth } = Dimensions.get('window');

interface FeedScreenProps {
  navigation: any;
}

const FeedScreen: React.FC<FeedScreenProps> = ({ navigation }) => {
  const [posts, setPosts] = useState<PostData[]>([]);
  const [stories, setStories] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      // TODO: Remplacer par de vraies données Supabase
      await loadPosts();
      await loadStories();
    } catch (error) {
      console.error('Erreur lors du chargement:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPosts = async () => {
    // Données de test - à remplacer par Supabase
    const mockPosts: PostData[] = [
      {
        id: '1',
        user_id: 'user1',
        content: 'Première publication sur Woézon ! 🎉',
        image_url: 'https://picsum.photos/400/400?random=1',
        post_type: 'post',
        likes_count: 42,
        comments_count: 8,
        shares_count: 3,
        views_count: 156,
        created_at: new Date().toISOString(),
        user: {
          id: 'user1',
          username: 'alice_dev',
          full_name: 'Alice Développeuse',
          avatar_url: 'https://picsum.photos/100/100?random=10',
          is_verified: true,
        },
      },
      {
        id: '2',
        user_id: 'user2',
        content: 'Belle journée pour coder ! ☀️💻',
        image_url: 'https://picsum.photos/400/600?random=2',
        post_type: 'post',
        likes_count: 28,
        comments_count: 5,
        shares_count: 1,
        views_count: 89,
        created_at: new Date(Date.now() - 3600000).toISOString(),
        user: {
          id: 'user2',
          username: 'bob_designer',
          full_name: 'Bob Designer',
          avatar_url: 'https://picsum.photos/100/100?random=11',
          is_verified: false,
        },
      },
    ];
    setPosts(mockPosts);
  };

  const loadStories = async () => {
    // Données de test - à remplacer par Supabase
    const mockStories: UserData[] = [
      {
        id: 'user1',
        username: 'alice_dev',
        full_name: 'Alice',
        avatar_url: 'https://picsum.photos/100/100?random=10',
        is_verified: true,
        is_private: false,
        followers_count: 1250,
        following_count: 890,
        posts_count: 45,
        friends_count: 156,
        is_online: true,
      },
      {
        id: 'user2',
        username: 'bob_designer',
        full_name: 'Bob',
        avatar_url: 'https://picsum.photos/100/100?random=11',
        is_verified: false,
        is_private: false,
        followers_count: 890,
        following_count: 567,
        posts_count: 32,
        friends_count: 89,
        is_online: false,
      },
    ];
    setStories(mockStories);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const loadMorePosts = async () => {
    if (loadingMore) return;
    
    setLoadingMore(true);
    // TODO: Charger plus de posts
    setTimeout(() => {
      setLoadingMore(false);
    }, 1000);
  };

  const handleLike = async (postId: string) => {
    // TODO: Implémenter le like
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, likes_count: post.likes_count + 1 }
          : post
      )
    );
  };

  const handleComment = (postId: string) => {
    navigation.navigate('Comments', { postId });
  };

  const handleShare = async (postId: string) => {
    // TODO: Implémenter le partage
    console.log('Partager post:', postId);
  };

  const handleUserPress = (userId: string) => {
    navigation.navigate('UserProfile', { userId });
  };

  const renderPost = ({ item }: { item: PostData }) => (
    <PostCard
      post={item}
      onLike={() => handleLike(item.id)}
      onComment={() => handleComment(item.id)}
      onShare={() => handleShare(item.id)}
      onUserPress={() => handleUserPress(item.user_id)}
      onPress={() => navigation.navigate('PostDetail', { postId: item.id })}
    />
  );

  const renderHeader = () => (
    <StoriesBar
      stories={stories}
      onStoryPress={(userId) => navigation.navigate('Stories', { userId })}
      onAddStory={() => navigation.navigate('Create')}
    />
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.loadingMore}>
        <LoadingSpinner size="small" />
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={styles.loadingText}>Chargement du feed...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={posts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={Colors.primary}
            colors={[Colors.primary]}
          />
        }
        onEndReached={loadMorePosts}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
        style={styles.feedList}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  feedList: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: FontSize.md,
    color: Colors.textSecondary,
  },
  loadingMore: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
});

export default FeedScreen;
