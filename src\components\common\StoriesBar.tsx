import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Constants
import { Colors } from '../../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../../constants/Layout';

// Types
import { UserData } from '../../types/navigation';

interface StoriesBarProps {
  stories: UserData[];
  onStoryPress: (userId: string) => void;
  onAddStory: () => void;
}

const StoriesBar: React.FC<StoriesBarProps> = ({
  stories,
  onStoryPress,
  onAddStory,
}) => {
  const renderAddStory = () => (
    <TouchableOpacity style={styles.storyContainer} onPress={onAddStory}>
      <View style={styles.addStoryContainer}>
        <Image
          source={{ uri: 'https://via.placeholder.com/60' }}
          style={styles.storyImage}
        />
        <View style={styles.addIcon}>
          <Ionicons name="add" size={16} color={Colors.background} />
        </View>
      </View>
      <Text style={styles.storyUsername}>Votre story</Text>
    </TouchableOpacity>
  );

  const renderStory = (user: UserData) => (
    <TouchableOpacity
      key={user.id}
      style={styles.storyContainer}
      onPress={() => onStoryPress(user.id)}
    >
      <View style={[styles.storyImageContainer, user.is_online && styles.onlineStory]}>
        <Image
          source={{ uri: user.avatar_url || 'https://via.placeholder.com/60' }}
          style={styles.storyImage}
        />
      </View>
      <Text style={styles.storyUsername} numberOfLines={1}>
        {user.username}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {renderAddStory()}
        {stories.map(renderStory)}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingVertical: Spacing.sm,
  },
  scrollContent: {
    paddingHorizontal: Spacing.md,
  },
  storyContainer: {
    alignItems: 'center',
    marginRight: Spacing.md,
    width: 70,
  },
  addStoryContainer: {
    position: 'relative',
  },
  storyImageContainer: {
    borderWidth: 2,
    borderColor: Colors.border,
    borderRadius: 32,
    padding: 2,
  },
  onlineStory: {
    borderColor: Colors.primary,
  },
  storyImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  addIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  storyUsername: {
    fontSize: FontSize.xs,
    color: Colors.text,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
});

export default StoriesBar;
