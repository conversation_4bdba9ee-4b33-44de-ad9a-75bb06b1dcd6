# 🛠️ Guide d'Environnement de Développement - Woézon

## 📋 Vue d'Ensemble

Ce document détaille l'environnement de développement complet pour l'application Woézon, incluant tous les outils, bibliothèques, et configurations nécessaires.

## 🏗️ Architecture Technique

### **Stack Principal**
- **Frontend** : React Native avec Expo
- **Backend** : Supabase (PostgreSQL + Auth + Storage)
- **IA** : OpenAI API
- **Langage** : TypeScript
- **Navigation** : React Navigation v6
- **État** : React Hooks + Context API

## 📦 Dépendances Principales

### **Core React Native**
```json
{
  "expo": "~49.0.0",
  "react": "18.2.0",
  "react-native": "0.72.6",
  "typescript": "^5.1.3"
}
```

**Utilité** : Base de l'application mobile cross-platform
**Pourquoi** : Expo facilite le développement et le déploiement

### **Navigation**
```json
{
  "@react-navigation/native": "^6.1.7",
  "@react-navigation/bottom-tabs": "^6.5.8",
  "@react-navigation/stack": "^6.3.17",
  "react-native-screens": "~3.22.0",
  "react-native-safe-area-context": "4.6.3"
}
```

**Utilité** : Navigation entre écrans avec animations fluides
**Pourquoi** : Standard de l'industrie pour React Native

### **Interface Utilisateur**
```json
{
  "@expo/vector-icons": "^13.0.0",
  "react-native-svg": "13.9.0",
  "react-native-reanimated": "~3.3.0",
  "styled-components": "^5.3.11"
}
```

**Utilité** : Icônes, animations, et styling avancé
**Pourquoi** : Interface moderne et fluide comme Instagram

### **Backend et Base de Données**
```json
{
  "@supabase/supabase-js": "^2.26.0"
}
```

**Utilité** : 
- Base de données PostgreSQL
- Authentification utilisateur
- Stockage de fichiers
- API temps réel
- Row Level Security

**Pourquoi** : Solution complète, sécurisée et scalable

### **Intelligence Artificielle**
```json
{
  "openai": "^3.3.0",
  "axios": "^1.4.0"
}
```

**Utilité** : 
- Ami virtuel IA
- Suggestions d'amis intelligentes
- Modération automatique de contenu
- Analyse de sentiment

**Pourquoi** : Fonctionnalités IA avancées pour différencier Woézon

## 🔧 Outils de Développement

### **Expo CLI**
```bash
npm install -g @expo/cli
```

**Utilité** : 
- Démarrage du serveur de développement
- Build et déploiement
- Gestion des dépendances natives

**Commandes principales** :
- `expo start` : Démarrer le serveur
- `expo build` : Créer un build
- `expo publish` : Publier une mise à jour

### **TypeScript**
```json
{
  "typescript": "^5.1.3",
  "@types/react": "~18.2.14",
  "@types/react-native": "~0.72.2"
}
```

**Utilité** : 
- Sécurité des types
- IntelliSense amélioré
- Détection d'erreurs à la compilation
- Meilleure maintenabilité

### **ESLint & Prettier**
```json
{
  "eslint": "^8.44.0",
  "prettier": "^2.8.8"
}
```

**Utilité** : 
- Qualité du code
- Formatage automatique
- Standards de codage cohérents

## 🗄️ Configuration Base de Données

### **Supabase Setup**
1. **Projet créé** : `woezon-app`
2. **URL** : `https://xvawtmzqbropjvvjboyy.supabase.co`
3. **Tables principales** :
   - `profiles` : Profils utilisateurs
   - `posts` : Publications
   - `friend_requests` : Demandes d'amitié
   - `friendships` : Amitiés
   - `follows` : Abonnements
   - `blocked_users` : Utilisateurs bloqués
   - `reports` : Signalements
   - `live_streams` : Lives
   - `calls` : Appels
   - `conversations` : Conversations
   - `messages` : Messages
   - `ai_companions` : Amis virtuels IA

### **Sécurité RLS**
- **Row Level Security** activé sur toutes les tables
- **Politiques** définies pour chaque type d'accès
- **Authentification** requise pour toutes les opérations

## 🌐 Variables d'Environnement

### **Fichier .env**
```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://xvawtmzqbropjvvjboyy.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# App Configuration
APP_NAME=Woézon
APP_VERSION=1.0.0

# OpenAI (à ajouter)
OPENAI_API_KEY=sk-...
```

**Sécurité** : 
- Clés publiques avec préfixe `EXPO_PUBLIC_`
- Clés privées stockées côté serveur uniquement
- Pas de commit des clés sensibles

## 📱 Configuration Mobile

### **iOS**
- **Version minimale** : iOS 11.0
- **Outils requis** : Xcode (macOS uniquement)
- **Certificats** : Apple Developer Account

### **Android**
- **API minimale** : Level 21 (Android 5.0)
- **Outils requis** : Android Studio
- **Keystore** : Pour signature des builds

### **Web**
- **Navigateurs supportés** : Chrome, Firefox, Safari, Edge
- **Responsive** : Adaptatif mobile/desktop

## 🚀 Scripts de Développement

### **package.json scripts**
```json
{
  "scripts": {
    "start": "expo start",
    "android": "expo start --android",
    "ios": "expo start --ios",
    "web": "expo start --web",
    "build": "expo build",
    "test": "jest",
    "lint": "eslint . --ext .ts,.tsx",
    "format": "prettier --write ."
  }
}
```

## 🔍 Débogage et Tests

### **Outils de Débogage**
- **Expo DevTools** : Interface web de débogage
- **React Native Debugger** : Débogage avancé
- **Flipper** : Inspection des composants
- **Console logs** : Logs en temps réel

### **Tests**
```json
{
  "jest": "^29.5.0",
  "@testing-library/react-native": "^12.1.2"
}
```

**Types de tests** :
- Tests unitaires des composants
- Tests d'intégration des services
- Tests E2E avec Detox

## 📊 Performance et Monitoring

### **Métriques**
- **Bundle size** : Optimisation du poids
- **Render time** : Performance des composants
- **Memory usage** : Gestion mémoire
- **Network requests** : Optimisation API

### **Outils**
- **Expo Analytics** : Métriques d'usage
- **Sentry** : Monitoring d'erreurs
- **Performance Monitor** : Métriques temps réel

## 🔒 Sécurité

### **Authentification**
- **Supabase Auth** : JWT tokens
- **Refresh tokens** : Renouvellement automatique
- **MFA** : Authentification multi-facteurs (futur)

### **Données**
- **Chiffrement** : HTTPS/TLS
- **Validation** : Côté client et serveur
- **Sanitization** : Protection XSS

## 🌍 Internationalisation

### **Support Multilingue**
```json
{
  "react-i18next": "^13.0.0",
  "i18next": "^23.2.0"
}
```

**Langues supportées** :
- Français (principal)
- Anglais (secondaire)

## 📈 Déploiement

### **Environnements**
- **Development** : Local avec Expo Go
- **Staging** : Expo Updates
- **Production** : App Stores + Web

### **CI/CD**
- **GitHub Actions** : Automatisation
- **Expo EAS** : Build et déploiement
- **Fastlane** : Automatisation mobile

---

**Note** : Ce document est mis à jour régulièrement avec l'évolution du projet.
