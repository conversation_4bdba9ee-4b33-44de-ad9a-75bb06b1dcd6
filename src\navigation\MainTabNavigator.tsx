import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, StyleSheet, Platform } from 'react-native';
import { Ionicons, MaterialIcons, Feather } from '@expo/vector-icons';

// Screens
import HomeStackNavigator from './stacks/HomeStackNavigator';
import SearchStackNavigator from './stacks/SearchStackNavigator';
import CreateStackNavigator from './stacks/CreateStackNavigator';
import ActivityStackNavigator from './stacks/ActivityStackNavigator';
import ProfileStackNavigator from './stacks/ProfileStackNavigator';

// Constants
import { Colors } from '../constants/Colors';
import { Layout, TabBarHeight } from '../constants/Layout';

// Types
import { MainTabParamList } from '../types/navigation';

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: Colors.text,
        tabBarInactiveTintColor: Colors.textSecondary,
        tabBarShowLabel: false,
        tabBarIcon: ({ focused, color, size }) => {
          return (
            <View style={[styles.iconContainer, focused && styles.iconContainerFocused]}>
              {getTabBarIcon(route.name, focused, color, size)}
            </View>
          );
        },
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeStackNavigator}
        options={{
          tabBarTestID: 'home-tab',
        }}
      />
      <Tab.Screen 
        name="Search" 
        component={SearchStackNavigator}
        options={{
          tabBarTestID: 'search-tab',
        }}
      />
      <Tab.Screen 
        name="Create" 
        component={CreateStackNavigator}
        options={{
          tabBarTestID: 'create-tab',
        }}
      />
      <Tab.Screen 
        name="Activity" 
        component={ActivityStackNavigator}
        options={{
          tabBarTestID: 'activity-tab',
          tabBarBadge: undefined, // TODO: Add notification count
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileStackNavigator}
        options={{
          tabBarTestID: 'profile-tab',
        }}
      />
    </Tab.Navigator>
  );
};

const getTabBarIcon = (routeName: string, focused: boolean, color: string, size: number) => {
  const iconSize = 24;
  
  switch (routeName) {
    case 'Home':
      return (
        <Ionicons
          name={focused ? 'home' : 'home-outline'}
          size={iconSize}
          color={color}
        />
      );
    case 'Search':
      return (
        <Ionicons
          name={focused ? 'search' : 'search-outline'}
          size={iconSize}
          color={color}
        />
      );
    case 'Create':
      return (
        <View style={[styles.createButton, focused && styles.createButtonFocused]}>
          <Ionicons
            name="add"
            size={iconSize}
            color={focused ? Colors.background : Colors.text}
          />
        </View>
      );
    case 'Activity':
      return (
        <Ionicons
          name={focused ? 'heart' : 'heart-outline'}
          size={iconSize}
          color={color}
        />
      );
    case 'Profile':
      return (
        <View style={[styles.profileIcon, focused && styles.profileIconFocused]}>
          <Ionicons
            name={focused ? 'person' : 'person-outline'}
            size={iconSize - 2}
            color={color}
          />
        </View>
      );
    default:
      return (
        <Ionicons
          name="help-outline"
          size={iconSize}
          color={color}
        />
      );
  }
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    height: TabBarHeight,
    paddingBottom: Platform.OS === 'ios' ? 20 : 10,
    paddingTop: 10,
    elevation: 8,
    shadowColor: Colors.text,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
  },
  iconContainerFocused: {
    // Animation ou effet pour l'icône active
  },
  createButton: {
    backgroundColor: Colors.background,
    borderWidth: 2,
    borderColor: Colors.text,
    borderRadius: 8,
    width: 28,
    height: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonFocused: {
    backgroundColor: Colors.text,
  },
  profileIcon: {
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  profileIconFocused: {
    borderColor: Colors.text,
    borderWidth: 2,
  },
});

export default MainTabNavigator;
